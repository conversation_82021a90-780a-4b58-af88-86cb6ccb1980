import React from "react";
import { Link, useLocation } from "react-router-dom";
import { createPageUrl } from "@/utils";
import { Leaf, Plus, Home, User } from "lucide-react";
import { Button } from "@/components/ui/button";
import { User as UserEntity } from "@/entities/User";

export default function Layout({ children, currentPageName }) {
  const location = useLocation();
  const [user, setUser] = React.useState(null);
  const [loading, setLoading] = React.useState(true);

  React.useEffect(() => {
    checkAuth();
  }, []);

  const checkAuth = async () => {
    try {
      const userData = await UserEntity.me();
      setUser(userData);
    } catch (error) {
      setUser(null);
    }
    setLoading(false);
  };

  const handleLogin = async () => {
    await UserEntity.loginWithRedirect(window.location.href);
  };

  const handleLogout = async () => {
    await UserEntity.logout();
    setUser(null);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-emerald-50 via-white to-teal-50">
      {/* Navigation */}
      <nav className="bg-white/80 backdrop-blur-md border-b border-emerald-100 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            {/* Logo */}
            <Link to={createPageUrl("Home")} className="flex items-center gap-3 group">
              <div className="w-10 h-10 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-xl flex items-center justify-center shadow-md group-hover:shadow-lg transition-shadow">
                <Leaf className="w-6 h-6 text-white" />
              </div>
              <div>
                <h1 className="text-xl font-bold text-gray-900">Net Zero Hub</h1>
                <p className="text-xs text-emerald-600 font-medium">Bookmark Bar</p>
              </div>
            </Link>

            {/* Navigation Links */}
            <div className="hidden md:flex items-center gap-1">
              <Link to={createPageUrl("Home")}>
                <Button 
                  variant={location.pathname === createPageUrl("Home") ? "default" : "ghost"}
                  className={location.pathname === createPageUrl("Home") ? "bg-emerald-500 hover:bg-emerald-600" : ""}
                  size="sm"
                >
                  <Home className="w-4 h-4 mr-2" />
                  Browse
                </Button>
              </Link>
              {user && (
                <Link to={createPageUrl("Submit")}>
                  <Button 
                    variant={location.pathname === createPageUrl("Submit") ? "default" : "ghost"}
                    className={location.pathname === createPageUrl("Submit") ? "bg-emerald-500 hover:bg-emerald-600" : ""}
                    size="sm"
                  >
                    <Plus className="w-4 h-4 mr-2" />
                    Submit
                  </Button>
                </Link>
              )}
            </div>

            {/* Auth Button */}
            <div className="flex items-center gap-3">
              {!loading && (
                user ? (
                  <div className="flex items-center gap-3">
                    <div className="hidden sm:block text-right">
                      <p className="text-sm font-medium text-gray-900">{user.full_name}</p>
                      <p className="text-xs text-gray-500">{user.email}</p>
                    </div>
                    <Button variant="outline" size="sm" onClick={handleLogout}>
                      Sign Out
                    </Button>
                  </div>
                ) : (
                  <Button onClick={handleLogin} size="sm" className="bg-emerald-500 hover:bg-emerald-600">
                    <User className="w-4 h-4 mr-2" />
                    Sign In
                  </Button>
                )
              )}
            </div>
          </div>
        </div>

        {/* Mobile Navigation */}
        <div className="md:hidden border-t border-emerald-100 bg-white/90">
          <div className="flex justify-around py-2">
            <Link to={createPageUrl("Home")}>
              <Button 
                variant={location.pathname === createPageUrl("Home") ? "default" : "ghost"}
                className={location.pathname === createPageUrl("Home") ? "bg-emerald-500 hover:bg-emerald-600" : ""}
                size="sm"
              >
                <Home className="w-4 h-4 mr-1" />
                Browse
              </Button>
            </Link>
            {user && (
              <Link to={createPageUrl("Submit")}>
                <Button 
                  variant={location.pathname === createPageUrl("Submit") ? "default" : "ghost"}
                  className={location.pathname === createPageUrl("Submit") ? "bg-emerald-500 hover:bg-emerald-600" : ""}
                  size="sm"
                >
                  <Plus className="w-4 h-4 mr-1" />
                  Submit
                </Button>
              </Link>
            )}
          </div>
        </div>
      </nav>

      {/* Main Content */}
      <main className="relative">
        {children}
      </main>

      {/* Footer */}
      <footer className="bg-white border-t border-emerald-100 mt-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center">
            <div className="flex items-center justify-center gap-2 mb-4">
              <div className="w-8 h-8 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-lg flex items-center justify-center">
                <Leaf className="w-5 h-5 text-white" />
              </div>
              <span className="text-lg font-semibold text-gray-900">Net Zero Hub</span>
            </div>
            <p className="text-gray-600 text-sm">
              Curating the best resources for a sustainable future
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
}
