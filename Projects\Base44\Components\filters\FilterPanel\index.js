// FilterPanel main file
import React, { useState } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { X, Filter } from "lucide-react";

const CATEGORIES = [
  { value: "energy", label: "Energy", color: "bg-yellow-100 text-yellow-800" },
  { value: "transport", label: "Transport", color: "bg-blue-100 text-blue-800" },
  { value: "waste", label: "Waste", color: "bg-green-100 text-green-800" },
  { value: "health", label: "Health", color: "bg-pink-100 text-pink-800" },
  { value: "policy", label: "Policy", color: "bg-indigo-100 text-indigo-800" },
  { value: "finance", label: "Finance", color: "bg-emerald-100 text-emerald-800" },
  { value: "technology", label: "Technology", color: "bg-cyan-100 text-cyan-800" },
  { value: "research", label: "Research", color: "bg-violet-100 text-violet-800" },
  { value: "education", label: "Education", color: "bg-amber-100 text-amber-800" },
  { value: "other", label: "Other", color: "bg-gray-100 text-gray-800" }
];

const TYPES = [
  { value: "event", label: "Events" },
  { value: "funding", label: "Funding" },
  { value: "resource", label: "Resources" },
  { value: "blog", label: "Blogs" },
  { value: "news", label: "News" }
];

export default function FilterPanel({ onClose, onFilterChange }) {
  const [selectedCategories, setSelectedCategories] = useState([]);
  const [selectedTypes, setSelectedTypes] = useState([]);

  const handleCategoryClick = (category) => {
    const newCategories = selectedCategories.includes(category)
      ? selectedCategories.filter(c => c !== category)
      : [...selectedCategories, category];
    
    setSelectedCategories(newCategories);
    onFilterChange({
      category: newCategories.length === 1 ? newCategories[0] : "all",
      type: selectedTypes.length === 1 ? selectedTypes[0] : "all"
    });
  };

  const handleTypeClick = (type) => {
    const newTypes = selectedTypes.includes(type)
      ? selectedTypes.filter(t => t !== type)
      : [...selectedTypes, type];
    
    setSelectedTypes(newTypes);
    onFilterChange({
      category: selectedCategories.length === 1 ? selectedCategories[0] : "all",
      type: newTypes.length === 1 ? newTypes[0] : "all"
    });
  };

  const clearFilters = () => {
    setSelectedCategories([]);
    setSelectedTypes([]);
    onFilterChange({ category: "all", type: "all" });
  };

  return (
    <Card className="mb-6 border-none shadow-lg bg-white/90 backdrop-blur-sm">
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle className="flex items-center gap-2">
          <Filter className="w-5 h-5 text-emerald-500" />
          Advanced Filters
        </CardTitle>
        <Button variant="ghost" size="icon" onClick={onClose}>
          <X className="w-4 h-4" />
        </Button>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Categories */}
        <div>
          <h3 className="font-semibold text-gray-900 mb-3">Categories</h3>
          <div className="flex flex-wrap gap-2">
            {CATEGORIES.map((category) => (
              <Badge
                key={category.value}
                className={`cursor-pointer transition-all ${
                  selectedCategories.includes(category.value)
                    ? category.color + " ring-2 ring-emerald-400"
                    : "bg-gray-100 text-gray-600 hover:bg-gray-200"
                }`}
                onClick={() => handleCategoryClick(category.value)}
              >
                {category.label}
              </Badge>
            ))}
          </div>
        </div>

        {/* Types */}
        <div>
          <h3 className="font-semibold text-gray-900 mb-3">Content Type</h3>
          <div className="flex flex-wrap gap-2">
            {TYPES.map((type) => (
              <Badge
                key={type.value}
                className={`cursor-pointer transition-all ${
                  selectedTypes.includes(type.value)
                    ? "bg-emerald-100 text-emerald-800 ring-2 ring-emerald-400"
                    : "bg-gray-100 text-gray-600 hover:bg-gray-200"
                }`}
                onClick={() => handleTypeClick(type.value)}
              >
                {type.label}
              </Badge>
            ))}
          </div>
        </div>

        {/* Clear Filters */}
        {(selectedCategories.length > 0 || selectedTypes.length > 0) && (
          <div className="pt-4 border-t border-gray-100">
            <Button variant="outline" onClick={clearFilters} size="sm">
              Clear All Filters
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
}