import React, { useState, useEffect, useCallback } from "react";
import { Card as CardEntity } from "@/entities/Card";
import { User } from "@/entities/User";
import { Search, Filter, Clock, TrendingUp, Calendar } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";

import CardComponent from "../Components/cards/CardComponent";
import FilterPanel from "../Components/filters/FilterPanel";
import HeroSection from "../Components/home/<USER>";
import FeaturedCards from "../Components/home/<USER>";

export default function HomePage() {
  const [cards, setCards] = useState([]);
  const [filteredCards, setFilteredCards] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("all");
  const [selectedType, setSelectedType] = useState("all");
  const [sortBy, setSortBy] = useState("newest");
  const [showFilters, setShowFilters] = useState(false);
  const [user, setUser] = useState(null);

  const filterCards = useCallback(() => {
    let filtered = [...cards];

    // Search filter
    if (searchQuery) {
      filtered = filtered.filter(card => 
        card.title?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        card.description?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        card.tags?.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))
      );
    }

    // Category filter
    if (selectedCategory !== "all") {
      filtered = filtered.filter(card => card.category === selectedCategory);
    }

    // Type filter
    if (selectedType !== "all") {
      filtered = filtered.filter(card => card.type === selectedType);
    }

    // Sort
    switch (sortBy) {
      case "newest":
        filtered.sort((a, b) => new Date(b.created_date) - new Date(a.created_date));
        break;
      case "trending":
        filtered.sort((a, b) => (b.click_count || 0) - (a.click_count || 0));
        break;
      case "closing":
        filtered = filtered.filter(card => card.closing_date).sort((a, b) => 
          new Date(a.closing_date) - new Date(b.closing_date)
        );
        break;
    }

    setFilteredCards(filtered);
  }, [cards, searchQuery, selectedCategory, selectedType, sortBy]);

  useEffect(() => {
    checkAuth();
    loadCards();
  }, []);

  useEffect(() => {
    filterCards();
  }, [filterCards]);

  const checkAuth = async () => {
    try {
      const userData = await User.me();
      setUser(userData);
    } catch (error) {
      setUser(null);
    }
  };

  const loadCards = async () => {
    try {
      const data = await CardEntity.list("-created_date");
      setCards(data);
    } catch (error) {
      console.error("Error loading cards:", error);
    }
    setLoading(false);
  };

  const handleCardClick = async (card) => {
    // Increment click count
    try {
      await CardEntity.update(card.id, { 
        click_count: (card.click_count || 0) + 1 
      });
      // Reload cards to reflect updated count
      loadCards();
    } catch (error) {
      console.error("Error updating click count:", error);
    }
  };

  return (
    <div className="min-h-screen">
      <HeroSection />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Featured Section */}
        <FeaturedCards 
          cards={cards.filter(card => card.is_featured)} 
          onCardClick={handleCardClick}
          user={user}
        />

        {/* Search and Filter Bar */}
        <div id="filter-section" className="bg-white rounded-2xl shadow-md border border-emerald-100 p-6 mb-8 scroll-mt-20">
          <div className="flex flex-col lg:flex-row gap-4">
            {/* Search */}
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <Input
                placeholder="Search resources, events, funding..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 h-12 border-gray-200 focus:border-emerald-300"
              />
            </div>

            {/* Quick Filters */}
            <div className="flex flex-wrap gap-3">
              <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                <SelectTrigger className="w-40">
                  <SelectValue placeholder="Category" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Categories</SelectItem>
                  <SelectItem value="energy">Energy</SelectItem>
                  <SelectItem value="transport">Transport</SelectItem>
                  <SelectItem value="waste">Waste</SelectItem>
                  <SelectItem value="health">Health</SelectItem>
                  <SelectItem value="policy">Policy</SelectItem>
                  <SelectItem value="finance">Finance</SelectItem>
                  <SelectItem value="technology">Technology</SelectItem>
                  <SelectItem value="research">Research</SelectItem>
                  <SelectItem value="education">Education</SelectItem>
                </SelectContent>
              </Select>

              <Select value={selectedType} onValueChange={setSelectedType}>
                <SelectTrigger className="w-32">
                  <SelectValue placeholder="Type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Types</SelectItem>
                  <SelectItem value="event">Events</SelectItem>
                  <SelectItem value="funding">Funding</SelectItem>
                  <SelectItem value="resource">Resources</SelectItem>
                  <SelectItem value="blog">Blogs</SelectItem>
                  <SelectItem value="news">News</SelectItem>
                </SelectContent>
              </Select>

              <Button
                variant="outline"
                onClick={() => setShowFilters(!showFilters)}
                className="border-emerald-200 text-emerald-700 hover:bg-emerald-50"
              >
                <Filter className="w-4 h-4 mr-2" />
                Filters
              </Button>
            </div>
          </div>

          {/* Sort Tabs */}
          <div className="flex justify-between items-center mt-6 pt-4 border-t border-gray-100">
            <Tabs value={sortBy} onValueChange={setSortBy}>
              <TabsList className="bg-emerald-50">
                <TabsTrigger value="newest" className="flex items-center gap-2">
                  <Clock className="w-4 h-4" />
                  Newest
                </TabsTrigger>
                <TabsTrigger value="trending" className="flex items-center gap-2">
                  <TrendingUp className="w-4 h-4" />
                  Trending
                </TabsTrigger>
                <TabsTrigger value="closing" className="flex items-center gap-2">
                  <Calendar className="w-4 h-4" />
                  Closing Soon
                </TabsTrigger>
              </TabsList>
            </Tabs>

            <p className="text-sm text-gray-500">
              {filteredCards.length} result{filteredCards.length !== 1 ? 's' : ''}
            </p>
          </div>
        </div>

        {/* Filter Panel */}
        {showFilters && (
          <FilterPanel 
            onClose={() => setShowFilters(false)}
            onFilterChange={(filters) => {
              setSelectedCategory(filters.category || "all");
              setSelectedType(filters.type || "all");
            }}
          />
        )}

        {/* Cards Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {loading ? (
            Array(8).fill(0).map((_, i) => (
              <div key={i} className="animate-pulse">
                <div className="bg-gray-200 rounded-2xl h-64"></div>
              </div>
            ))
          ) : (
            filteredCards.map((card) => (
              <CardComponent
                key={card.id}
                card={card}
                onClick={() => handleCardClick(card)}
                user={user}
              />
            ))
          )}
        </div>

        {/* Empty State */}
        {!loading && filteredCards.length === 0 && (
          <div className="text-center py-16">
            <div className="w-16 h-16 bg-emerald-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <Search className="w-8 h-8 text-emerald-600" />
            </div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2">No results found</h3>
            <p className="text-gray-500 mb-6">Try adjusting your search or filters</p>
            <Button 
              onClick={() => {
                setSearchQuery("");
                setSelectedCategory("all");
                setSelectedType("all");
              }}
              variant="outline"
              className="border-emerald-200 text-emerald-700 hover:bg-emerald-50"
            >
              Clear filters
            </Button>
          </div>
        )}
      </div>
    </div>
  );
}