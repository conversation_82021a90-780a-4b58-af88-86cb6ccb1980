// CardComponent main file

import React from "react";
import { format } from "date-fns";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { 
  Calendar, 
  DollarSign, 
  ExternalLink, 
  MapPin, 
  TrendingUp,
  FileText,
  Clock,
  User as UserIcon,
  ArrowUp,
  ArrowDown
} from "lucide-react";
import { User } from "@/entities/User";
import { Vote } from "@/entities/Vote";
import { Card as CardEntity } from "@/entities/Card";

import CardDetailModal from "./CardDetailModal";

const typeIcons = {
  event: Calendar,
  funding: DollarSign,
  resource: FileText,
  blog: FileText,
  news: FileText
};

const typeColors = {
  event: "bg-blue-100 text-blue-800 border-blue-200",
  funding: "bg-green-100 text-green-800 border-green-200",
  resource: "bg-purple-100 text-purple-800 border-purple-200",
  blog: "bg-orange-100 text-orange-800 border-orange-200",
  news: "bg-red-100 text-red-800 border-red-200"
};

const categoryColors = {
  energy: "bg-yellow-100 text-yellow-800",
  transport: "bg-blue-100 text-blue-800",
  waste: "bg-green-100 text-green-800",
  health: "bg-pink-100 text-pink-800",
  policy: "bg-indigo-100 text-indigo-800",
  finance: "bg-emerald-100 text-emerald-800",
  technology: "bg-cyan-100 text-cyan-800",
  research: "bg-violet-100 text-violet-800",
  education: "bg-amber-100 text-amber-800",
  other: "bg-gray-100 text-gray-800"
};

export default function CardComponent({ card, onClick, user }) {
  const [showModal, setShowModal] = React.useState(false);
  const [imageError, setImageError] = React.useState(false);
  const [vote, setVote] = React.useState(null);
  const [counts, setCounts] = React.useState({ 
    up: card.upvote_count || 0, 
    down: card.downvote_count || 0 
  });
  const TypeIcon = typeIcons[card.type] || FileText;

  React.useEffect(() => {
    const fetchVote = async () => {
        if (user) {
            try {
                const votes = await Vote.filter({ card_id: card.id, user_email: user.email });
                if (votes.length > 0) {
                    setVote(votes[0]);
                }
            } catch(e) {
                console.error("Failed to fetch vote", e);
            }
        }
    };
    fetchVote();
  }, [user, card.id]);
  
  const handleCardBodyClick = () => {
    if (onClick) onClick(card);
    if (user) {
      setShowModal(true);
    } else {
      handleAuthRequired();
    }
  };

  const handleAuthRequired = async () => {
    await User.loginWithRedirect(window.location.href);
  };

  const handleVote = async (e, type) => {
    e.stopPropagation(); // Prevent modal from opening
    if (!user) {
        handleAuthRequired();
        return;
    }

    let newCounts = { ...counts };
    
    // User is toggling off their vote
    if (vote && vote.vote_type === type) {
        await Vote.delete(vote.id);
        setVote(null);
        newCounts[type]--;
    } 
    // User is changing their vote
    else if (vote && vote.vote_type !== type) {
        const newVote = await Vote.update(vote.id, { vote_type: type });
        setVote(newVote);
        newCounts[type]++;
        newCounts[type === 'up' ? 'down' : 'up']--;
    } 
    // User is casting a new vote
    else {
        const newVote = await Vote.create({
            card_id: card.id,
            user_email: user.email,
            vote_type: type
        });
        setVote(newVote);
        newCounts[type]++;
    }

    setCounts(newCounts);
    await CardEntity.update(card.id, {
        upvote_count: newCounts.up,
        downvote_count: newCounts.down
    });
  };

  const getCardImage = () => {
    if (card.image_url) return card.image_url;

    // Fallback gradients based on category
    const gradients = {
      energy: "bg-gradient-to-br from-yellow-400 to-orange-500",
      transport: "bg-gradient-to-br from-blue-400 to-indigo-500",
      waste: "bg-gradient-to-br from-green-400 to-emerald-500",
      health: "bg-gradient-to-br from-pink-400 to-rose-500",
      policy: "bg-gradient-to-br from-indigo-400 to-purple-500",
      finance: "bg-gradient-to-br from-emerald-400 to-teal-500",
      technology: "bg-cyan-400 to-blue-500",
      research: "bg-violet-400 to-purple-500",
      education: "bg-amber-400 to-yellow-500",
      other: "bg-gray-400 to-slate-500"
    };

    return gradients[card.category] || gradients.other;
  };

  const formatDate = (dateString) => {
    try {
      return format(new Date(dateString), "MMM d, yyyy");
    } catch {
      return dateString;
    }
  };

  return (
    <>
      <Card 
        onClick={handleCardBodyClick}
        className="group cursor-pointer transition-all duration-300 hover:shadow-xl hover:-translate-y-1 bg-white border border-gray-100 overflow-hidden flex flex-col"
      >
        <div className="relative">
          {/* Image or Gradient */}
          {card.image_url && !imageError ? (
            <div className="aspect-video overflow-hidden bg-gray-100">
              <img
                src={card.image_url}
                alt={card.title}
                className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                onError={() => setImageError(true)}
              />
            </div>
          ) : (
            <div className={`aspect-video flex items-center justify-center ${getCardImage()}`}>
              <TypeIcon className="w-8 h-8 text-white opacity-80" />
            </div>
          )}

          {/* Type Badge */}
          <Badge className={`absolute top-3 left-3 ${typeColors[card.type]} border font-medium`}>
            <TypeIcon className="w-3 h-3 mr-1" />
            {card.type.charAt(0).toUpperCase() + card.type.slice(1)}
          </Badge>

          {/* Click Count */}
          {card.click_count > 0 && (
            <Badge variant="secondary" className="absolute top-3 right-3 bg-white/90">
              <TrendingUp className="w-3 h-3 mr-1" />
              {card.click_count}
            </Badge>
          )}
        </div>

        <CardContent className="p-6 flex flex-col flex-1">
          {/* Title */}
          <h3 className="font-bold text-lg text-gray-900 mb-2 line-clamp-2 group-hover:text-emerald-600 transition-colors">
            {card.title}
          </h3>

          {/* Description */}
          <p className="text-gray-600 text-sm mb-4 line-clamp-3">
            {card.description}
          </p>

          {/* Key Info */}
          <div className="space-y-2 mb-4">
            {/* Event Date */}
            {card.type === "event" && card.event_date && (
              <div className="flex items-center gap-2 text-sm text-gray-500">
                <Calendar className="w-4 h-4" />
                <span>{formatDate(card.event_date)}</span>
                {card.event_type && (
                  <Badge variant="outline" className="text-xs">
                    <MapPin className="w-3 h-3 mr-1" />
                    {card.event_type}
                  </Badge>
                )}
              </div>
            )}

            {/* Location for Face-to-Face or Hybrid Events */}
            {card.type === "event" && (card.event_type === "face-to-face" || card.event_type === "hybrid") && (card.city || card.venue_name) && (
              <div className="flex items-start gap-2 text-sm text-gray-500">
                <MapPin className="w-4 h-4 mt-0.5" />
                <div className="flex-1">
                  {card.venue_name && <div className="font-medium">{card.venue_name}</div>}
                  {(card.city || card.country) && (
                    <div>{[card.city, card.country].filter(Boolean).join(", ")}</div>
                  )}
                </div>
              </div>
            )}
            
            {/* Funding Amount */}
            {card.type === "funding" && card.funding_amount && (
              <div className="flex items-center gap-2 text-sm text-gray-500">
                <DollarSign className="w-4 h-4" />
                <span className="font-medium">{card.funding_amount}</span>
              </div>
            )}

            {/* Closing Date */}
            {card.closing_date && (
              <div className="flex items-center gap-2 text-sm text-red-600">
                <Clock className="w-4 h-4" />
                <span>Closes {formatDate(card.closing_date)}</span>
              </div>
            )}

            {/* Author/Publisher */}
            {(card.author || card.publisher) && (
              <div className="flex items-center gap-2 text-sm text-gray-500">
                <UserIcon className="w-4 h-4" />
                <span>{card.author || card.publisher}</span>
              </div>
            )}
          </div>

          {/* Category and Tags */}
          <div className="flex flex-wrap gap-2 mb-4">
            <Badge className={categoryColors[card.category]}>
              {card.category}
            </Badge>
            {card.tags?.slice(0, 2).map((tag, index) => (
              <Badge key={index} variant="outline" className="text-xs">
                {tag}
              </Badge>
            ))}
            {card.tags?.length > 2 && (
              <Badge variant="outline" className="text-xs">
                +{card.tags.length - 2}
              </Badge>
            )}
          </div>
          
          <div className="flex-grow" />

          {/* Action Buttons */}
          <div className="flex items-center gap-2 mt-4">
            <Button 
              size="sm"
              className="w-full bg-emerald-500 hover:bg-emerald-600 text-white group-hover:bg-emerald-600 flex-1"
            >
              {user ? "View Details" : "Sign in to View"}
              <ExternalLink className="w-4 h-4 ml-2" />
            </Button>
            {user && (
              <div className="flex items-center border border-gray-200 rounded-md">
                <Button 
                  size="icon" 
                  variant="ghost" 
                  onClick={(e) => handleVote(e, 'up')}
                  className={`rounded-r-none ${vote?.vote_type === 'up' ? 'bg-emerald-100 text-emerald-600' : ''}`}
                >
                  <ArrowUp className="w-4 h-4" />
                </Button>
                <span className="px-2 text-sm font-medium text-gray-600 border-l border-r">
                  {counts.up - counts.down}
                </span>
                <Button 
                  size="icon" 
                  variant="ghost" 
                  onClick={(e) => handleVote(e, 'down')}
                  className={`rounded-l-none ${vote?.vote_type === 'down' ? 'bg-red-100 text-red-600' : ''}`}
                >
                  <ArrowDown className="w-4 h-4" />
                </Button>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Detail Modal */}
      {showModal && user && (
        <CardDetailModal
          card={card}
          isOpen={showModal}
          onClose={() => setShowModal(false)}
        />
      )}
    </>
  );
}
