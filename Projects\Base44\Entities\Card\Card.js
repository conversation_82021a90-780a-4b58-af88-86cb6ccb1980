// Card entity main file
{
  "name": "Card",
  "type": "object",
  "properties": {
    "url": {
      "type": "string",
      "description": "The original URL submitted"
    },
    "title": {
      "type": "string",
      "description": "Title of the content"
    },
    "description": {
      "type": "string",
      "description": "Brief description of the content"
    },
    "image_url": {
      "type": "string",
      "description": "Thumbnail or featured image URL"
    },
    "type": {
      "type": "string",
      "enum": [
        "event",
        "funding",
        "resource",
        "blog",
        "news"
      ],
      "description": "Type of content"
    },
    "category": {
      "type": "string",
      "enum": [
        "energy",
        "transport",
        "waste",
        "health",
        "policy",
        "finance",
        "technology",
        "research",
        "education",
        "other"
      ],
      "description": "Main category"
    },
    "tags": {
      "type": "array",
      "items": {
        "type": "string"
      },
      "description": "User-defined tags"
    },
    "event_date": {
      "type": "string",
      "format": "date-time",
      "description": "For events: date and time"
    },
    "event_type": {
      "type": "string",
      "enum": [
        "online",
        "hybrid",
        "face-to-face"
      ],
      "description": "For events: format type"
    },
    "venue_name": {
      "type": "string",
      "description": "For face-to-face events: venue or location name"
    },
    "address": {
      "type": "string",
      "description": "For face-to-face events: street address"
    },
    "city": {
      "type": "string",
      "description": "For face-to-face events: city"
    },
    "country": {
      "type": "string",
      "description": "For face-to-face events: country"
    },
    "funding_amount": {
      "type": "string",
      "description": "For funding: amount available"
    },
    "closing_date": {
      "type": "string",
      "format": "date",
      "description": "For funding/events: application or event deadline"
    },
    "publisher": {
      "type": "string",
      "description": "For resources: publisher name"
    },
    "author": {
      "type": "string",
      "description": "For blogs/news: author name"
    },
    "publish_date": {
      "type": "string",
      "format": "date",
      "description": "For resources/blogs: publication date"
    },
    "resource_type": {
      "type": "string",
      "enum": [
        "report",
        "toolkit",
        "guide",
        "whitepaper",
        "case-study",
        "other"
      ],
      "description": "For resources: specific type"
    },
    "click_count": {
      "type": "number",
      "default": 0,
      "description": "Number of times card was clicked"
    },
    "is_featured": {
      "type": "boolean",
      "default": false,
      "description": "Whether this card is featured"
    },
    "upvote_count": {
      "type": "number",
      "default": 0,
      "description": "Total number of upvotes"
    },
    "downvote_count": {
      "type": "number",
      "default": 0,
      "description": "Total number of downvotes"
    }
  },
  "required": [
    "url",
    "title",
    "type",
    "category"
  ]
}