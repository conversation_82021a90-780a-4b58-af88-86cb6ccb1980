// URLInput main file

import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Spark<PERSON>, Loader2 } from "lucide-react";

export default function URLInput({ onSubmit, loading }) {
  const [url, setUrl] = useState("");

  const handleSubmit = (e) => {
    e.preventDefault();
    if (url.trim()) {
      onSubmit(url.trim());
    }
  };

  const isValidUrl = (string) => {
    try {
      new URL(string);
      return true;
    } catch (_) {
      return false;
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="text-center mb-8">
        <div className="w-16 h-16 bg-emerald-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <Sparkles className="w-8 h-8 text-emerald-600" />
        </div>
        <p className="text-gray-600 text-lg">
          Paste the URL of the resource you want to share with the community
        </p>
      </div>

      <div className="space-y-3">
        <Label htmlFor="url" className="text-lg font-medium">
          Resource URL
        </Label>
        <Input
          id="url"
          type="url"
          value={url}
          onChange={(e) => setUrl(e.target.value)}
          placeholder="https://example.com/amazing-resource"
          className="h-14 text-lg border-gray-200 focus:border-emerald-300 focus:ring-emerald-300"
          disabled={loading}
        />
        {url && !isValidUrl(url) && (
          <p className="text-red-500 text-sm">Please enter a valid URL</p>
        )}
      </div>

      <Button
        type="submit"
        disabled={!url.trim() || !isValidUrl(url) || loading}
        className="w-full h-14 text-lg bg-emerald-500 hover:bg-emerald-600 disabled:opacity-50"
      >
        {loading ? (
          <>
            <Loader2 className="w-5 h-5 mr-2 animate-spin" />
            Analyzing URL...
          </>
        ) : (
          <>
            <Sparkles className="w-5 h-5 mr-2" />
            Click to begin the magic
          </>
        )}
      </Button>

      {/* Examples */}
      <div className="mt-8 p-4 bg-emerald-50 rounded-xl">
        <h4 className="font-semibold text-emerald-800 mb-2">Example URLs:</h4>
        <div className="text-sm text-emerald-700 space-y-1">
          <div>• Event: https://eventbrite.com/climate-summit-2024</div>
          <div>• Funding: https://grants.gov/clean-energy-funding</div>
          <div>• Resource: https://iea.org/net-zero-roadmap</div>
        </div>
      </div>
    </form>
  );
}
