// SubmissionForm main file

import React, { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Plus, X, Save, ArrowLeft, MapPin } from "lucide-react";

const CATEGORIES = [
  "energy", "transport", "waste", "health", "policy", 
  "finance", "technology", "research", "education", "other"
];

const TYPES = ["event", "funding", "resource", "blog", "news"];

const EVENT_TYPES = ["online", "hybrid", "face-to-face"];

const RESOURCE_TYPES = [
  "report", "toolkit", "guide", "whitepaper", "case-study", "other"
];

export default function SubmissionForm({ initialData, onSubmit, onBack, loading }) {
  const [formData, setFormData] = useState({
    title: initialData.title || "",
    description: initialData.description || "",
    image_url: initialData.image_url || "",
    type: initialData.type || "resource",
    category: initialData.category || "other",
    tags: initialData.suggested_tags || [],
    event_date: initialData.event_date ? new Date(initialData.event_date).toISOString().slice(0, 16) : "",
    event_type: initialData.event_type || "",
    venue_name: initialData.venue_name || "",
    address: initialData.address || "",
    city: initialData.city || "",
    country: initialData.country || "",
    funding_amount: initialData.funding_amount || "",
    closing_date: initialData.closing_date || "",
    publisher: initialData.publisher || "",
    author: initialData.author || "",
    publish_date: initialData.publish_date || "",
    resource_type: initialData.resource_type || "",
    is_featured: false
  });

  const [newTag, setNewTag] = useState("");

  const handleInputChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const addTag = () => {
    if (newTag.trim() && !formData.tags.includes(newTag.trim())) {
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, newTag.trim()]
      }));
      setNewTag("");
    }
  };

  const removeTag = (tagToRemove) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    const cleanedData = { ...formData };
    
    // Remove empty values
    Object.keys(cleanedData).forEach(key => {
      if (cleanedData[key] === "" || cleanedData[key] === null) {
        delete cleanedData[key];
      }
    });

    onSubmit(cleanedData);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-8">
      {/* Basic Information */}
      <Card>
        <CardHeader>
          <CardTitle>Basic Information</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="type">Content Type *</Label>
              <Select value={formData.type} onValueChange={(value) => handleInputChange("type", value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {TYPES.map(type => (
                    <SelectItem key={type} value={type}>
                      {type.charAt(0).toUpperCase() + type.slice(1)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="category">Category *</Label>
              <Select value={formData.category} onValueChange={(value) => handleInputChange("category", value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {CATEGORIES.map(category => (
                    <SelectItem key={category} value={category}>
                      {category.charAt(0).toUpperCase() + category.slice(1)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="title">Title *</Label>
            <Input
              id="title"
              value={formData.title}
              onChange={(e) => handleInputChange("title", e.target.value)}
              placeholder="Resource title"
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Description *</Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) => handleInputChange("description", e.target.value)}
              placeholder="Brief description of the resource"
              className="h-24"
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="image_url">Image URL</Label>
            <Input
              id="image_url"
              type="url"
              value={formData.image_url}
              onChange={(e) => handleInputChange("image_url", e.target.value)}
              placeholder="https://example.com/image.jpg"
            />
          </div>
        </CardContent>
      </Card>

      {/* Type-specific Fields */}
      {formData.type === "event" && (
        <Card>
          <CardHeader>
            <CardTitle>Event Details</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="event_date">Event Date</Label>
                <Input
                  id="event_date"
                  type="datetime-local"
                  value={formData.event_date}
                  onChange={(e) => handleInputChange("event_date", e.target.value)}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="event_type">Event Format</Label>
                <Select value={formData.event_type} onValueChange={(value) => handleInputChange("event_type", value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select format" />
                  </SelectTrigger>
                  <SelectContent>
                    {EVENT_TYPES.map(type => (
                      <SelectItem key={type} value={type}>
                        {type.charAt(0).toUpperCase() + type.slice(1).replace('-', ' ')}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Location Fields - Show for face-to-face or hybrid events */}
            {(formData.event_type === "face-to-face" || formData.event_type === "hybrid") && (
              <div className="space-y-4 p-4 bg-purple-50 rounded-lg">
                <h4 className="font-semibold text-purple-900 flex items-center gap-2">
                  <MapPin className="w-4 h-4" />
                  Location Details (for physical attendance)
                </h4>
                
                <div className="space-y-2">
                  <Label htmlFor="venue_name">Venue Name</Label>
                  <Input
                    id="venue_name"
                    value={formData.venue_name}
                    onChange={(e) => handleInputChange("venue_name", e.target.value)}
                    placeholder="Conference center, hotel, etc."
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="address">Address</Label>
                  <Input
                    id="address"
                    value={formData.address}
                    onChange={(e) => handleInputChange("address", e.target.value)}
                    placeholder="Street address"
                  />
                </div>

                <div className="grid md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="city">City</Label>
                    <Input
                      id="city"
                      value={formData.city}
                      onChange={(e) => handleInputChange("city", e.target.value)}
                      placeholder="City"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="country">Country</Label>
                    <Input
                      id="country"
                      value={formData.country}
                      onChange={(e) => handleInputChange("country", e.target.value)}
                      placeholder="Country"
                    />
                  </div>
                </div>
              </div>
            )}

            <div className="space-y-2">
              <Label htmlFor="closing_date">Registration Deadline</Label>
              <Input
                id="closing_date"
                type="date"
                value={formData.closing_date}
                onChange={(e) => handleInputChange("closing_date", e.target.value)}
              />
            </div>
          </CardContent>
        </Card>
      )}

      {formData.type === "funding" && (
        <Card>
          <CardHeader>
            <CardTitle>Funding Details</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="funding_amount">Funding Amount</Label>
                <Input
                  id="funding_amount"
                  value={formData.funding_amount}
                  onChange={(e) => handleInputChange("funding_amount", e.target.value)}
                  placeholder="e.g., $50,000 - $100,000"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="closing_date">Application Deadline</Label>
                <Input
                  id="closing_date"
                  type="date"
                  value={formData.closing_date}
                  onChange={(e) => handleInputChange("closing_date", e.target.value)}
                />
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {formData.type === "resource" && (
        <Card>
          <CardHeader>
            <CardTitle>Resource Details</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="publisher">Publisher</Label>
                <Input
                  id="publisher"
                  value={formData.publisher}
                  onChange={(e) => handleInputChange("publisher", e.target.value)}
                  placeholder="Organization or company"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="resource_type">Resource Type</Label>
                <Select value={formData.resource_type} onValueChange={(value) => handleInputChange("resource_type", value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select type" />
                  </SelectTrigger>
                  <SelectContent>
                    {RESOURCE_TYPES.map(type => (
                      <SelectItem key={type} value={type}>
                        {type.charAt(0).toUpperCase() + type.slice(1).replace('-', ' ')}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="publish_date">Publication Date</Label>
              <Input
                id="publish_date"
                type="date"
                value={formData.publish_date}
                onChange={(e) => handleInputChange("publish_date", e.target.value)}
              />
            </div>
          </CardContent>
        </Card>
      )}

      {(formData.type === "blog" || formData.type === "news") && (
        <Card>
          <CardHeader>
            <CardTitle>{formData.type === "blog" ? "Blog" : "News"} Details</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="author">Author</Label>
                <Input
                  id="author"
                  value={formData.author}
                  onChange={(e) => handleInputChange("author", e.target.value)}
                  placeholder="Author name"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="publisher">Publisher</Label>
                <Input
                  id="publisher"
                  value={formData.publisher}
                  onChange={(e) => handleInputChange("publisher", e.target.value)}
                  placeholder="Publication or website"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="publish_date">Publication Date</Label>
              <Input
                id="publish_date"
                type="date"
                value={formData.publish_date}
                onChange={(e) => handleInputChange("publish_date", e.target.value)}
              />
            </div>
          </CardContent>
        </Card>
      )}

      {/* Tags */}
      <Card>
        <CardHeader>
          <CardTitle>Tags</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex flex-wrap gap-2 mb-4">
            {formData.tags.map((tag, index) => (
              <Badge key={index} variant="secondary" className="flex items-center gap-1">
                {tag}
                <X 
                  className="w-3 h-3 cursor-pointer hover:text-red-500" 
                  onClick={() => removeTag(tag)}
                />
              </Badge>
            ))}
          </div>

          <div className="flex gap-2">
            <Input
              value={newTag}
              onChange={(e) => setNewTag(e.target.value)}
              placeholder="Add a tag"
              onKeyPress={(e) => e.key === "Enter" && (e.preventDefault(), addTag())}
            />
            <Button type="button" onClick={addTag} size="icon" variant="outline">
              <Plus className="w-4 h-4" />
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Featured Toggle */}
      <Card>
        <CardContent className="flex items-center justify-between p-6">
          <div>
            <Label htmlFor="featured" className="text-base font-medium">
              Feature this resource
            </Label>
            <p className="text-sm text-gray-500 mt-1">
              Featured resources appear prominently on the homepage
            </p>
          </div>
          <Switch
            id="featured"
            checked={formData.is_featured}
            onCheckedChange={(checked) => handleInputChange("is_featured", checked)}
          />
        </CardContent>
      </Card>

      {/* Action Buttons */}
      <div className="flex gap-4">
        <Button type="button" variant="outline" onClick={onBack} className="flex-1">
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back
        </Button>
        <Button 
          type="submit" 
          disabled={loading || !formData.title || !formData.description}
          className="flex-1 bg-emerald-500 hover:bg-emerald-600"
        >
          {loading ? (
            "Saving..."
          ) : (
            <>
              <Save className="w-4 h-4 mr-2" />
              Submit Resource
            </>
          )}
        </Button>
      </div>
    </form>
  );
}
