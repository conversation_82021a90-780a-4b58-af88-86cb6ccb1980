// CardDetailModal main file

import React from "react";
import { format } from "date-fns";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { 
  Calendar, 
  DollarSign, 
  ExternalLink, 
  MapPin, 
  User as UserIcon,
  FileText,
  Clock,
  Share2
} from "lucide-react";

const typeIcons = {
  event: Calendar,
  funding: DollarSign,
  resource: FileText,
  blog: FileText,
  news: FileText
};

const categoryColors = {
  energy: "bg-yellow-100 text-yellow-800",
  transport: "bg-blue-100 text-blue-800",
  waste: "bg-green-100 text-green-800",
  health: "bg-pink-100 text-pink-800",
  policy: "bg-indigo-100 text-indigo-800",
  finance: "bg-emerald-100 text-emerald-800",
  technology: "bg-cyan-100 text-cyan-800",
  research: "bg-violet-100 text-violet-800",
  education: "bg-amber-100 text-amber-800",
  other: "bg-gray-100 text-gray-800"
};

export default function CardDetailModal({ card, isOpen, onClose }) {
  const [imageError, setImageError] = React.useState(false);
  const TypeIcon = typeIcons[card.type] || FileText;

  const formatDate = (dateString) => {
    try {
      return format(new Date(dateString), "EEEE, MMMM d, yyyy");
    } catch {
      return dateString;
    }
  };

  const formatDateTime = (dateString) => {
    try {
      return format(new Date(dateString), "EEEE, MMMM d, yyyy 'at' h:mm a");
    } catch {
      return dateString;
    }
  };

  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: card.title,
          text: card.description,
          url: card.url,
        });
      } catch (error) {
        console.log('Error sharing:', error);
      }
    } else {
      // Fallback to clipboard
      navigator.clipboard.writeText(card.url);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-3 text-xl">
            <div className="p-2 bg-emerald-100 rounded-lg">
              <TypeIcon className="w-5 h-5 text-emerald-600" />
            </div>
            <div>
              <div className="flex items-center gap-2">
                <span>{card.title}</span>
                <Badge className={categoryColors[card.category]}>
                  {card.category}
                </Badge>
              </div>
            </div>
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Featured Image */}
          {card.image_url && !imageError && (
            <div className="aspect-video rounded-xl overflow-hidden bg-gray-200">
              <img 
                src={card.image_url} 
                alt={card.title}
                className="w-full h-full object-cover"
                onError={() => setImageError(true)}
              />
            </div>
          )}

          {/* Description */}
          <div>
            <h3 className="font-semibold text-gray-900 mb-2">Description</h3>
            <p className="text-gray-700 leading-relaxed">{card.description}</p>
          </div>

          {/* Key Details */}
          <div className="grid md:grid-cols-2 gap-4">
            {/* Event Details */}
            {card.type === "event" && (
              <>
                {card.event_date && (
                  <div className="flex items-center gap-3 p-3 bg-blue-50 rounded-lg">
                    <Calendar className="w-5 h-5 text-blue-600" />
                    <div>
                      <p className="text-sm font-medium text-blue-900">Event Date</p>
                      <p className="text-sm text-blue-700">{formatDateTime(card.event_date)}</p>
                    </div>
                  </div>
                )}
                {card.event_type && (
                  <div className="flex items-center gap-3 p-3 bg-purple-50 rounded-lg">
                    <MapPin className="w-5 h-5 text-purple-600" />
                    <div>
                      <p className="text-sm font-medium text-purple-900">Format</p>
                      <p className="text-sm text-purple-700 capitalize">{card.event_type}</p>
                    </div>
                  </div>
                )}
              </>
            )}

            {/* Location Details for Face-to-Face or Hybrid Events */}
            {(card.event_type === "face-to-face" || card.event_type === "hybrid") && (card.venue_name || card.address || card.city || card.country) && (
              <div className="md:col-span-2 p-4 bg-purple-50 rounded-lg">
                <div className="flex items-start gap-3">
                  <MapPin className="w-5 h-5 text-purple-600 mt-0.5" />
                  <div>
                    <p className="text-sm font-medium text-purple-900 mb-2">Location (Physical Attendance)</p>
                    <div className="space-y-1 text-sm text-purple-700">
                      {card.venue_name && <div className="font-medium">{card.venue_name}</div>}
                      {card.address && <div>{card.address}</div>}
                      {(card.city || card.country) && (
                        <div>{[card.city, card.country].filter(Boolean).join(", ")}</div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Funding Details */}
            {card.type === "funding" && (
              <>
                {card.funding_amount && (
                  <div className="flex items-center gap-3 p-3 bg-green-50 rounded-lg">
                    <DollarSign className="w-5 h-5 text-green-600" />
                    <div>
                      <p className="text-sm font-medium text-green-900">Funding Amount</p>
                      <p className="text-sm text-green-700 font-semibold">{card.funding_amount}</p>
                    </div>
                  </div>
                )}
              </>
            )}

            {/* Closing Date */}
            {card.closing_date && (
              <div className="flex items-center gap-3 p-3 bg-red-50 rounded-lg">
                <Clock className="w-5 h-5 text-red-600" />
                <div>
                  <p className="text-sm font-medium text-red-900">
                    {card.type === "funding" ? "Application Deadline" : "Closing Date"}
                  </p>
                  <p className="text-sm text-red-700">{formatDate(card.closing_date)}</p>
                </div>
              </div>
            )}

            {/* Author/Publisher */}
            {(card.author || card.publisher) && (
              <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                <UserIcon className="w-5 h-5 text-gray-600" />
                <div>
                  <p className="text-sm font-medium text-gray-900">
                    {card.author ? "Author" : "Publisher"}
                  </p>
                  <p className="text-sm text-gray-700">{card.author || card.publisher}</p>
                </div>
              </div>
            )}
          </div>

          {/* Additional Details */}
          {(card.resource_type || card.publish_date) && (
            <div className="space-y-3">
              <h3 className="font-semibold text-gray-900">Additional Information</h3>
              <div className="grid md:grid-cols-2 gap-4">
                {card.resource_type && (
                  <div>
                    <p className="text-sm font-medium text-gray-500">Resource Type</p>
                    <p className="text-sm text-gray-900 capitalize">{card.resource_type.replace(/-/g, ' ')}</p>
                  </div>
                )}
                {card.publish_date && (
                  <div>
                    <p className="text-sm font-medium text-gray-500">Published</p>
                    <p className="text-sm text-gray-900">{formatDate(card.publish_date)}</p>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Tags */}
          {card.tags && card.tags.length > 0 && (
            <div>
              <h3 className="font-semibold text-gray-900 mb-3">Tags</h3>
              <div className="flex flex-wrap gap-2">
                {card.tags.map((tag, index) => (
                  <Badge key={index} variant="outline">
                    {tag}
                  </Badge>
                ))}
              </div>
            </div>
          )}

          <Separator />

          {/* Actions */}
          <div className="flex gap-3">
            <Button 
              asChild 
              className="flex-1 bg-emerald-500 hover:bg-emerald-600"
            >
              <a 
                href={card.url} 
                target="_blank" 
                rel="noopener noreferrer"
                className="flex items-center justify-center gap-2"
              >
                <ExternalLink className="w-4 h-4" />
                Visit Resource
              </a>
            </Button>
            <Button variant="outline" onClick={handleShare}>
              <Share2 className="w-4 h-4 mr-2" />
              Share
            </Button>
          </div>

          {/* Meta Info */}
          <div className="text-xs text-gray-400 text-center pt-4 border-t">
            Added {formatDate(card.created_date)} • Viewed {card.click_count || 0} times
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
