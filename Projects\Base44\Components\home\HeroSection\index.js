// HeroSection main file

import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON> } from "react-router-dom";
import { createPageUrl } from "@/utils";
import { Plus, Sparkles, Users, Globe } from "lucide-react";

export default function HeroSection() {
  const handleExploreClick = () => {
    const filterSection = document.getElementById("filter-section");
    if (filterSection) {
      filterSection.scrollIntoView({ behavior: "smooth" });
    }
  };

  return (
    <div className="relative overflow-hidden bg-gradient-to-br from-emerald-500 via-teal-600 to-green-700">
      <div className="absolute inset-0 bg-[url('https://images.unsplash.com/photo-1611273426858-450d8e3c9fce?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80')] opacity-10 bg-cover bg-center" />
      
      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
        <div className="text-center text-white">
          <div className="flex items-center justify-center gap-2 mb-6">
            <Sparkles className="w-8 h-8 text-emerald-200" />
            <h1 className="text-5xl md:text-6xl font-bold">
              Net Zero Hub
            </h1>
            <Sparkles className="w-8 h-8 text-emerald-200" />
          </div>
          
          <p className="text-xl md:text-2xl text-emerald-100 mb-8 max-w-3xl mx-auto leading-relaxed">
            Discover, share, and bookmark the best resources for building a sustainable future. 
            From funding opportunities to cutting-edge research – all in one place.
          </p>
          
          {/* Stats */}
          <div className="flex flex-wrap justify-center gap-8 mb-10 text-emerald-100">
            <div className="flex items-center gap-2">
              <Globe className="w-5 h-5" />
              <span className="text-lg">Global Resources</span>
            </div>
            <div className="flex items-center gap-2">
              <Users className="w-5 h-5" />
              <span className="text-lg">Community Driven</span>
            </div>
            <div className="flex items-center gap-2">
              <Sparkles className="w-5 h-5" />
              <span className="text-lg">AI Powered</span>
            </div>
          </div>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button 
              size="lg" 
              className="bg-white text-emerald-600 hover:bg-emerald-50 font-semibold px-8 py-3 text-lg"
              asChild
            >
              <Link to={createPageUrl("Submit")}>
                <Plus className="w-5 h-5 mr-2" />
                Submit Resource
              </Link>
            </Button>
            
            <Button 
              size="lg" 
              variant="ghost"
              className="border border-white text-white hover:bg-white hover:text-emerald-600 font-semibold transition-colors"
              onClick={handleExploreClick}
            >
              Explore Categories
            </Button>
          </div>
        </div>
      </div>
      
      {/* Floating Elements */}
      <div className="absolute top-20 left-10 w-20 h-20 bg-white/10 rounded-full blur-xl animate-pulse" />
      <div className="absolute bottom-20 right-10 w-32 h-32 bg-emerald-300/20 rounded-full blur-2xl animate-pulse delay-1000" />
      <div className="absolute top-1/2 left-1/4 w-16 h-16 bg-teal-300/20 rounded-full blur-xl animate-pulse delay-2000" />
    </div>
  );
}
