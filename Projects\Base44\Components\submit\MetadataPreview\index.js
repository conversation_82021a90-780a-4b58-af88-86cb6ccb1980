// MetadataPreview main file

import React from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  CheckCircle, 
  Edit, 
  Calendar, 
  DollarSign, 
  User as UserIcon,
  Building,
  Tag,
  RefreshCw,
  Send,
  MapPin
} from "lucide-react";

const typeColors = {
  event: "bg-blue-100 text-blue-800",
  funding: "bg-green-100 text-green-800",
  resource: "bg-purple-100 text-purple-800",
  blog: "bg-orange-100 text-orange-800",
  news: "bg-red-100 text-red-800"
};

const categoryColors = {
  energy: "bg-yellow-100 text-yellow-800",
  transport: "bg-blue-100 text-blue-800",
  waste: "bg-green-100 text-green-800",
  health: "bg-pink-100 text-pink-800",
  policy: "bg-indigo-100 text-indigo-800",
  finance: "bg-emerald-100 text-emerald-800",
  technology: "bg-cyan-100 text-cyan-800",
  research: "bg-violet-100 text-violet-800",
  education: "bg-amber-100 text-amber-800",
  other: "bg-gray-100 text-gray-800"
};

export default function MetadataPreview({ data, url, onEdit, onDirectSubmit, onStartOver, loading }) {
  const [imageError, setImageError] = React.useState(false);
  
  return (
    <div className="space-y-6">
      <div className="text-center">
        <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <CheckCircle className="w-8 h-8 text-green-600" />
        </div>
        <h3 className="text-2xl font-bold text-gray-900 mb-2">Metadata Extracted!</h3>
        <p className="text-gray-600">
          Our AI has analyzed your URL. If everything looks correct, you can submit it directly.
        </p>
      </div>

      {/* Preview Card */}
      <Card className="border-2 border-emerald-200 bg-emerald-50/50">
        <CardHeader>
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <CardTitle className="text-xl mb-2">{data.title}</CardTitle>
              <div className="flex gap-2 mb-3">
                <Badge className={typeColors[data.type]}>
                  {data.type.charAt(0).toUpperCase() + data.type.slice(1)}
                </Badge>
                <Badge className={categoryColors[data.category]}>
                  {data.category.charAt(0).toUpperCase() + data.category.slice(1)}
                </Badge>
              </div>
            </div>
            {data.image_url && !imageError ? (
              <div className="ml-4">
                <img 
                  src={data.image_url} 
                  alt="Preview" 
                  className="w-20 h-20 object-cover rounded-lg bg-gray-200"
                  onError={() => setImageError(true)}
                />
              </div>
            ) : null }
          </div>
        </CardHeader>
        <CardContent>
          <p className="text-gray-700 mb-4">{data.description}</p>
          
          {/* Extracted Details */}
          <div className="grid md:grid-cols-2 gap-4 mb-4">
            {data.event_date && (
              <div className="flex items-center gap-2 text-sm">
                <Calendar className="w-4 h-4 text-blue-500" />
                <span>Event: {new Date(data.event_date).toLocaleDateString()}</span>
              </div>
            )}
            
            {/* Location Information for Face-to-Face Events */}
            {data.event_type === "face-to-face" && (data.venue_name || data.city || data.country || data.address) && (
              <div className="flex items-start gap-2 text-sm col-span-2">
                <MapPin className="w-4 h-4 text-purple-500 mt-0.5" />
                <div>
                  <span className="font-medium">Location: </span>
                  <div className="text-gray-600">
                    {data.venue_name && <div>{data.venue_name}</div>}
                    {data.address && <div>{data.address}</div>}
                    {(data.city || data.country) && (
                      <div>{[data.city, data.country].filter(Boolean).join(", ")}</div>
                    )}
                  </div>
                </div>
              </div>
            )}
            
            {data.funding_amount && (
              <div className="flex items-center gap-2 text-sm">
                <DollarSign className="w-4 h-4 text-green-500" />
                <span>Amount: {data.funding_amount}</span>
              </div>
            )}
            
            {data.author && (
              <div className="flex items-center gap-2 text-sm">
                <UserIcon className="w-4 h-4 text-gray-500" />
                <span>Author: {data.author}</span>
              </div>
            )}
            
            {data.publisher && (
              <div className="flex items-center gap-2 text-sm">
                <Building className="w-4 h-4 text-gray-500" />
                <span>Publisher: {data.publisher}</span>
              </div>
            )}
            
            {data.closing_date && (
              <div className="flex items-center gap-2 text-sm">
                <Calendar className="w-4 h-4 text-red-500" />
                <span>Closes: {new Date(data.closing_date).toLocaleDateString()}</span>
              </div>
            )}
          </div>
          
          {/* Tags */}
          {data.suggested_tags && data.suggested_tags.length > 0 && (
            <div className="flex items-center gap-2 mb-4">
              <Tag className="w-4 h-4 text-gray-400" />
              <div className="flex flex-wrap gap-1">
                {data.suggested_tags.map((tag, index) => (
                  <Badge key={index} variant="outline" className="text-xs">
                    {tag}
                  </Badge>
                ))}
              </div>
            </div>
          )}
          
          {/* Original URL */}
          <div className="text-xs text-gray-500 truncate">
            Source: {url}
          </div>
        </CardContent>
      </Card>

      {/* Action Buttons */}
      <div className="grid grid-cols-1 sm:grid-cols-3 gap-3">
        <Button
          variant="outline"
          onClick={onStartOver}
          className="w-full"
          disabled={loading}
        >
          <RefreshCw className="w-4 h-4 mr-2" />
          Reset
        </Button>
        <Button
          variant="outline"
          onClick={onEdit}
          className="w-full"
          disabled={loading}
        >
          <Edit className="w-4 h-4 mr-2" />
          Edit Details
        </Button>
        <Button
          onClick={onDirectSubmit}
          className="w-full bg-emerald-500 hover:bg-emerald-600"
          disabled={loading}
        >
          <Send className="w-4 h-4 mr-2" />
          Submit
        </Button>
      </div>
    </div>
  );
}
