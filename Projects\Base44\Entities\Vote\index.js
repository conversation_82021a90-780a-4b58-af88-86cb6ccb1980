// Vote entity main file
{
  "name": "Vote",
  "type": "object",
  "properties": {
    "card_id": {
      "type": "string",
      "description": "The ID of the card being voted on"
    },
    "user_email": {
      "type": "string",
      "description": "The email of the user who voted"
    },
    "vote_type": {
      "type": "string",
      "enum": [
        "up",
        "down"
      ],
      "description": "The type of vote"
    }
  },
  "required": [
    "card_id",
    "user_email",
    "vote_type"
  ]
}