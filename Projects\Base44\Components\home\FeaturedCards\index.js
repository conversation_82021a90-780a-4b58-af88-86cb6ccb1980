// FeaturedCards main file
import React from "react";
import { Star } from "lucide-react";
import CardComponent from "../cards/CardComponent";

export default function FeaturedCards({ cards, onCardClick, user }) {
  if (!cards || cards.length === 0) return null;

  return (
    <div className="mb-12">
      <div className="flex items-center gap-2 mb-6">
        <Star className="w-6 h-6 text-yellow-500 fill-current" />
        <h2 className="text-2xl font-bold text-gray-900">Featured Resources</h2>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {cards.slice(0, 3).map((card) => (
          <CardComponent
            key={card.id}
            card={card}
            onClick={onCardClick}
            user={user}
          />
        ))}
      </div>
    </div>
  );
}